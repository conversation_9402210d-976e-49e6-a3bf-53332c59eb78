## 程序说明文档

### 1. 程序概述
本程序旨在提供一个学生学业数据管理与分析平台，支持学生学期专业成绩的显示、排名，并提供详细的学业分析功能。同时，系统具备灵活的配置管理、数据导入导出及日志记录能力，以满足不同用户角色的需求。
### 3. 技术栈与设计
*   **图表绘制**: Echarts
*   **后端**: Django
*   **前端**: Vue.js
*   **整体设计风格**: 美观现代化
*   **整体框图配色**: 蓝白色调

### 2. 主要功能模块

#### 2.1 排行榜显示
*   **默认显示**：学号、姓名、班级、专业、综合素质、成绩、总分、总排名。
*   **可选显示**:等其他数据库内有关成绩的信息
*   **排序功能**：
    *   在特定学年、学期、专业下，可根据任何字段进行排序。
    *   支持多字段组合排序。
*   **数据导出**：提供导出接口，可将查询结果导出。
*   **AI辅助功能**：
    *   排行榜旁边设有AI辅助功能，管理员或老师可与AI对话，利用其SQL知识生成SQL查询语句。
    *   生成的SQL语句可直接复制到代码框中，或点击按钮直接运用到SQL接口中。
    *   提供两个接口：一个是分析，另一个是应用。

#### 2.2 详情与分析
*   **详情显示**：点击"详情"按钮，显示该学期的详细数据。
*   **学业分析**：
    *   点击"分析"按钮，展示该学生所有学期的学业排名或成绩曲线，以及综合成绩排名或曲线。
    *   数据以表格形式呈现，与排行榜形式一致。
    *   **智能分析**：点击后，调用大语言模型对该学生的情况进行分析，并在下方形成分析报告。

#### 2.3 主面板
*   **数据概览**：显示数据库信息，包括总学期数、总人数（以学号作为唯一标志）、各个年级的人数占比等。

#### 2.4 API类型管理系统界面
*   **API Key管理**：
    *   **当前使用模型**: deepseek-r1
    *   **模型密钥**: ***********************************
    *   显示Token的总输入量。
    *   显示Key的状态。
    *   支持温度系数调整。
    *   支持最大输入量设置
    *   Prompt设计:分析Prompt和SQL Prompt等
    *   支持API Key管理。

#### 2.5 数据库日志系统界面
*   **日志显示**：显示数据库的变更记录。
*   **回溯功能**：支持数据回溯。

#### 2.6 导入导出模块
*   **导入功能**：支持两种模块导入（例如：xlsx）。
*   **导出功能**：支持下载。
*   **记录管理**：提供导入记录和历史记录查询，类似日志系统。
*   **撤销与恢复**：同时支持撤销导入与恢复。

#### 2.7 班级分析页面
*   **班级概览分析**：
    *   用户可选择学年、学期和专业，系统将展示该专业下各班级的平均分、均值等统计信息。
    *   提供可视化的图表展示，以便直观对比各班级表现。
*   **班级内部详情分析**：
    *   可进一步查看某个具体班级的学生成绩分布信息，提供统计学意义上的分析。
*   **AI智能分析**：
    *   利用AI对班级在该学期的考试情况、综合素质等多种信息进行深度分析，生成分析报告。

#### 2.8 用户管理
*   **用户角色管理**：管理员可管理所有用户角色。
*   **教师权限**：支持对教师进行授权，分配"子管理员"权限。
*   **学生账户**：提供学生账户的密码重置等操作。
### 4. 用户角色与权限

本系统根据用户角色设定不同的访问权限和操作能力，确保数据安全与功能合理分配。所有密码修改操作均需用户输入原密码进行验证。

*   **管理员**：
    *   **密码管理**：可修改所有用户的密码，或进行一键重置；也可修改自己的密码。
    *   **反馈管理**：可查询并回复用户反馈信息。
    *   **系统管理**：
        *   可访问数据库日志系统，查看数据变更记录。
        *   可访问API类型管理系统，管理AI模型配置（如API Key、温度系数、最大输入量、Prompt设计等）。
    *   **数据与权限管理**：
        *   可进行数据导入、导出操作。
        *   可进行权限管理，包括授予或撤销教师的"子管理员"权限。
    *   **页面访问**：拥有所有系统页面的访问权限。
*   **教师**：
    *   **密码管理**：可修改自己的密码。
    *   **反馈管理**：可访问反馈界面，提交反馈并查询管理员的回复。
    *   **系统访问限制**：
        *   默认不可访问API类型管理系统界面和数据库日志系统界面。
        *   默认不能进行数据导入、修改和删除操作。
        *   **子管理员权限**：经管理员授权后，可获得部分导入、修改和删除权限，成为"子管理员"；根管理员可随时撤销此权限。
*   **学生**：
    *   **密码管理**：可修改自己的密码。
    *   **反馈管理**：可访问反馈界面，提交反馈并查询管理员的回复。
    *   **个人信息访问**：仅可查看自己的学业详情与分析报告。
